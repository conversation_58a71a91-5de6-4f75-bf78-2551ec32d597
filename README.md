# KiFlow Mobile 📱

KiFlow Mobile - це інтерактивна освітня платформа, розроблена на React Native з використанням Expo. Додаток надає користувачам доступ до різноманітних курсів через мобільний інтерфейс з підтримкою відео-уроків, інтерактивних квізів та AI-чату.

## 🚀 Основні функції

- **Управління курсами**: Перегляд та доступ до освітніх курсів
- **Інтерактивне навчання**: Різні типи контенту включаючи:
  - Відео-уроки з підтримкою Mux Player
  - Текстовий контент з порадами та прикладами
  - Інтерактивні квізи
  - Відкриті питання
- **Аутентифікація користувачів**: Безпечна система входу та реєстрації
- **Профілі користувачів**: Персоналізований досвід користування
- **AI-чат**: Інтеграція з Google Gemini для інтелектуальної підтримки
- **Симулятор нерухомості**: Спеціалізований модуль для навчання продажам
- **Адміністративна панель**: Інструменти для створення та управління контентом

## 🛠 Технічний стек

### Основні технології
- **Framework**: React Native 0.79.4 з Expo 53.0.12
- **Navigation**: Expo Router з файловою маршрутизацією
- **UI Framework**: Gluestack UI v2 з NativeWind (Tailwind CSS)
- **State Management**: Zustand + TanStack Query
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI Integration**: Google Gemini API
- **Video Player**: Mux Player React
- **Icons**: Lucide React Native

### Інструменти розробки
- **Language**: TypeScript 5.8.3
- **Testing**: Jest, Playwright, Storybook
- **Linting**: ESLint з Prettier
- **CI/CD**: GitHub Actions
- **Error Tracking**: Sentry
- **Package Manager**: npm

## 📋 Передумови

Перед початком роботи переконайтеся, що у вас встановлено:

- **Node.js** (версія 18 або новіша)
- **npm** або **yarn**
- **Expo CLI**: `npm install -g @expo/cli`
- **Git**

### Для web розробки (працює):
- **Сучасний браузер** (Chrome, Firefox, Safari, Edge)

### Для мобільної розробки (наразі не працює):
- **Android Studio** (для Android емулятора) - потребує налаштування
- **Xcode** (для iOS симулятора, тільки на macOS) - потребує налаштування
- ~~**Expo Go** додаток~~ - не підтримується через нативні залежності

## ⚙️ Налаштування середовища

### 1. Клонування репозиторію
```bash
git clone https://github.com/shepitchakpavlo/kitflow_mobile.git
cd kiflow_mobile
```

### 2. Встановлення залежностей
```bash
npm install
```

### 3. Налаштування змінних середовища

Проект використовує змінні середовища для безпечного управління API ключами. Детальні інструкції знаходяться в [ENVIRONMENT_SETUP.md](./ENVIRONMENT_SETUP.md).

**Швидке налаштування:**
```bash
# Скопіюйте приклад файлу середовища (якщо існує)
cp .env.example .env

# Відредагуйте .env файл з вашими реальними API ключами
```

**Необхідні змінні:**
- `SUPABASE_URL`: URL вашого Supabase проекту
- `SUPABASE_ANON_KEY`: Публічний ключ Supabase
- `GEMINI_API_KEY`: API ключ Google Gemini (для серверної частини)

### 4. Налаштування бази даних

Проект використовує Supabase як основну базу даних. Детальна інформація про структуру бази даних знаходиться в [database_structure.md](./database_structure.md).

**Застосування міграцій:**
```bash
# Виконайте міграції через Supabase Dashboard або CLI
# Файли міграцій знаходяться в папці database/migrations/
```

**Заповнення тестовими даними:**
```bash
npm run db:seed
```

## 🚀 Запуск проекту

### ⚠️ Поточний стан проекту

**Важливо:** На даний момент проект **не запускається на мобільних пристроях** (Android/iOS). Працює тільки **web версія**.

### Режими запуску

**✅ Web версія (працює):**
```bash
# Запуск web версії
npm run web
# або
expo start --web
```

**❌ Мобільні платформи (наразі не працюють):**
```bash
# Android (не працює)
npm run android

# iOS (не працює)
npm run ios

# Expo Go (не працює)
npm start
```

### Варіанти відкриття додатку

**✅ Працюючі варіанти:**

1. **Web браузер** (рекомендовано):
   ```bash
   npm run web
   ```
   - Автоматично відкриється в браузері на `http://localhost:8081`
   - Повний функціонал додатку доступний через web

**❌ Не працюючі варіанти (потребують виправлення):**

1. **Expo Go**:
   - Наразі не підтримується через конфлікти залежностей
   - Потребує налаштування development build

2. **Development Build**:
   - Потребує створення спеціальної збірки
   - Необхідно вирішити проблеми з нативними залежностями

3. **Емулятори/Симулятори**:
   - Android емулятор - не працює
   - iOS симулятор - не працює

### 🔧 Відомі проблеми та їх вирішення

**Основні проблеми:**
1. **Нативні залежності**: Деякі пакети потребують development build
2. **Expo Go обмеження**: Проект використовує пакети, несумісні з Expo Go
3. **Конфігурація**: Потребує налаштування для мобільних платформ

**Для запуску на мобільних пристроях потрібно:**
1. Створити development build для платформи
2. Встановити створений build на пристрій
3. Вирішити конфлікти залежностей

### 🚨 Troubleshooting

**Якщо проект не запускається на мобільних:**
1. **Перевірте залежності**: Деякі пакети потребують нативної компіляції
2. **Використовуйте web версію**: Поки мобільні платформи налаштовуються
3. **Створіть development build**: Потребує додаткового налаштування нативних залежностей

**Якщо web версія не працює:**
1. Перевірте, чи встановлені всі залежності: `npm install`
2. Очистіть кеш: `expo start --clear`
3. Перевірте порт 8081: `lsof -ti:8081 | xargs kill -9`

## 📁 Структура проекту

```
kiflow_mobile/
├── src/
│   ├── app/                    # Expo Router сторінки (файлова маршрутизація)
│   │   ├── auth/              # Сторінки аутентифікації
│   │   ├── admin/             # Адміністративні сторінки
│   │   ├── api/               # API маршрути (серверні функції)
│   │   ├── profile/           # Профіль користувача
│   │   └── real-estate-simulator/ # Симулятор нерухомості
│   ├── components/            # React компоненти
│   │   ├── ui/               # Базові UI компоненти
│   │   ├── features/         # Функціональні компоненти
│   │   └── screens/          # Компоненти екранів
│   ├── services/             # API сервіси та бізнес-логіка
│   ├── hooks/                # Custom React hooks
│   ├── types/                # TypeScript типи
│   ├── utils/                # Утилітарні функції
│   ├── config/               # Конфігурація (Supabase, тощо)
│   ├── constants/            # Константи додатку
│   ├── data/                 # Статичні дані та моки
│   └── assets/               # Зображення, шрифти, відео
├── database/                 # Міграції та функції БД
├── tests/                    # E2E тести (Playwright)
├── .storybook/              # Конфігурація Storybook
├── docs/                    # Документація проекту
└── config files             # Конфігураційні файли
```

## 🗄️ База даних

### Основні таблиці
- `courses` - Інформація про курси
- `CourseSlides` - Слайди курсів
- `slide_ai_prompts` - AI промпти для слайдів

### Управління міграціями
```bash
# Міграції знаходяться в database/migrations/
# Застосовуйте їх через Supabase Dashboard або CLI

# Приклад застосування через Supabase CLI:
supabase db push
```

### Row Level Security (RLS)
Проект використовує RLS політики для контролю доступу до даних. Політики налаштовуються автоматично через міграції.

## 🧪 Тестування

### Unit тести (Jest)
```bash
# Запуск unit тестів
npm test

# Запуск з watch режимом
npm run test
```

### E2E тести (Playwright)
```bash
# Запуск E2E тестів
npm run playwright-ci

# Запуск з UI режимом
npm run playwright-ui
```

## 🏗️ Збірка та деплой

### ✅ Працюючі збірки

**Web збірка (працює):**
```bash
# Збірка для web
expo export:web

# Запуск web версії
npm run web
```

**Перевірка якості коду:**
```bash
# Перевірка типів
npm run type-check

# Лінтинг
npm run lint
```

### ⚠️ Мобільні збірки (потребують налаштування)

**Для мобільних платформ:**
- Наразі потребують додаткового налаштування через нативні залежності
- Необхідно вирішити конфлікти залежностей
- Рекомендується використовувати web версію для розробки

### CI/CD з GitHub Actions

Проект налаштований для автоматичної збірки та деплою через GitHub Actions:

**Автоматичні процеси:**
- ESLint перевірка якості коду
- TypeScript перевірка типів
- Збірка Expo проекту для web
- Перевірка на витік чутливих API ключів
- Завантаження source maps в Sentry

## 🛠️ Корисні команди для розробки

```bash
# Очистка кешу
expo start --clear

# Перевірка типів без збірки
npm run type-check

# Форматування коду
npx prettier --write .

# Аналіз bundle розміру
npx expo export:web --analyze

# Генерація типів з Supabase
supabase gen types typescript --project-id dduvsjouorgwvmxipiuc > src/types/supabase.ts
```

## 🔧 Налаштування IDE

### VS Code (рекомендовано)
Встановіть наступні розширення:
- ES7+ React/Redux/React-Native snippets
- TypeScript Importer
- Prettier - Code formatter
- ESLint
- Expo Tools
- Tailwind CSS IntelliSense

### Налаштування TypeScript
Проект використовує строгі налаштування TypeScript. Конфігурація знаходиться в `tsconfig.json`.

## 🐛 Відстеження помилок

Проект інтегрований з **Sentry** для моніторингу помилок:
- Автоматичне відстеження помилок у продакшні
- Моніторинг продуктивності
- Завантаження source maps при деплої
- Відстеження релізів з інформацією про коміти

## 📚 Додаткові ресурси

### Документація
- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [Supabase Documentation](https://supabase.com/docs)
- [Gluestack UI Documentation](https://ui.gluestack.io/)
- [TanStack Query Documentation](https://tanstack.com/query)

### Внутрішня документація
- [Environment Setup](./ENVIRONMENT_SETUP.md) - Детальне налаштування середовища
- [Database Structure](./database_structure.md) - Структура бази даних
- [Database README](./database/README.md) - Управління міграціями
- [Refactoring Recommendations](./src/docs/refactoring_recommendations.md) - Рекомендації з рефакторингу

## 🤝 Внесок у проект

1. Форкніть репозиторій
2. Створіть feature гілку (`git checkout -b feature/amazing-feature`)
3. Зробіть коміт змін (`git commit -m 'Add amazing feature'`)
4. Запушіть в гілку (`git push origin feature/amazing-feature`)
5. Відкрийте Pull Request

### Правила розробки
- Дотримуйтесь існуючого стилю коду
- Пишіть тести для нового функціоналу
- Оновлюйте документацію при необхідності
- Перевіряйте, що всі тести проходять
- Тестуйте на web версії (поки мобільні платформи не працюють)

**Поточний статус:** ✅ Web працює | ⚠️ Мобільні в розробці

**Розроблено з ❤️ командою KiFlow**
